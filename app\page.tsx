"use client"

import { <PERSON>, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Settings, FileText, CheckSquare } from "lucide-react"
import Link from "next/link"

export default function HomePage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b px-4 py-4 flex items-center justify-between">
        <h1 className="text-xl font-semibold">首页</h1>
        <Settings className="w-6 h-6 text-gray-600" />
      </div>

      <div className="p-4 space-y-6">
        {/* Overview Section */}
        <div>
          <h2 className="text-2xl font-bold mb-4">概览</h2>
          <div className="grid grid-cols-2 gap-4">
            <Card>
              <CardContent className="p-6">
                <div className="text-sm text-gray-600 mb-2">待审批</div>
                <div className="text-3xl font-bold">3</div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-6">
                <div className="text-sm text-gray-600 mb-2">到期任务</div>
                <div className="text-3xl font-bold">5</div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Latest Announcement */}
        <Card>
          <CardContent className="p-6">
            <div className="text-sm text-gray-600 mb-2">最新公告</div>
            <div className="text-xl font-semibold">新政策更新</div>
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <div>
          <h2 className="text-2xl font-bold mb-4">快速操作</h2>
          <div className="grid grid-cols-2 gap-4">
            <Button className="h-12 text-base">提交费用</Button>
            <Button variant="outline" className="h-12 text-base">
              查看报告
            </Button>
          </div>
        </div>

        {/* Recent Activity */}
        <div>
          <h2 className="text-2xl font-bold mb-4">最近活动</h2>
          <div className="space-y-4">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                <FileText className="w-6 h-6 text-gray-600" />
              </div>
              <div className="flex-1">
                <div className="font-medium">费用报告</div>
                <div className="text-sm text-blue-600">2天前提交</div>
              </div>
            </div>
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                <CheckSquare className="w-6 h-6 text-gray-600" />
              </div>
              <div className="flex-1">
                <div className="font-medium">跟进客户</div>
                <div className="text-sm text-gray-600">3天后到期</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Navigation */}
      <div className="fixed bottom-0 left-0 right-0 bg-white border-t">
        <div className="grid grid-cols-4">
          <Link href="/" className="flex flex-col items-center py-3 text-blue-600">
            <div className="w-6 h-6 mb-1">🏠</div>
            <span className="text-xs">首页</span>
          </Link>
          <Link href="/tasks" className="flex flex-col items-center py-3 text-gray-600">
            <div className="w-6 h-6 mb-1">✓</div>
            <span className="text-xs">任务</span>
          </Link>
          <Link href="/reports" className="flex flex-col items-center py-3 text-gray-600">
            <div className="w-6 h-6 mb-1">📊</div>
            <span className="text-xs">报告</span>
          </Link>
          <Link href="/profile" className="flex flex-col items-center py-3 text-gray-600">
            <div className="w-6 h-6 mb-1">👤</div>
            <span className="text-xs">个人</span>
          </Link>
        </div>
      </div>
    </div>
  )
}
