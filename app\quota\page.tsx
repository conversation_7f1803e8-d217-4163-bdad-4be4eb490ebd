"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowLeft } from "lucide-react"
import Link from "next/link"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"

export default function QuotaPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b px-4 py-4 flex items-center">
        <Link href="/">
          <ArrowLeft className="w-6 h-6 mr-4" />
        </Link>
        <h1 className="text-xl font-semibold">配额管理</h1>
      </div>

      <div className="p-4 space-y-6">
        {/* Dealer Quota */}
        <div>
          <h2 className="text-2xl font-bold mb-4">经销商配额</h2>

          <div className="bg-white p-4 rounded-lg">
            <div className="flex items-center gap-4 mb-6">
              <Avatar className="w-16 h-16">
                <AvatarImage src="/placeholder.svg" />
                <AvatarFallback className="bg-green-800 text-white">AM</AvatarFallback>
              </Avatar>
              <div>
                <div className="text-xl font-semibold">ABC Motors</div>
                <div className="text-blue-600">经销商 ID: 12345</div>
              </div>
            </div>

            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-blue-600">总配额</span>
                <span className="font-semibold">1000 units</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-blue-600">剩余配额</span>
                <span className="font-semibold">200 units</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-blue-600">已分配配额</span>
                <span className="font-semibold">800 units</span>
              </div>
            </div>
          </div>
        </div>

        {/* Quota Allocation */}
        <div>
          <h2 className="text-2xl font-bold mb-4">配额分配</h2>

          <div className="space-y-4">
            <div className="bg-white p-4 rounded-lg">
              <div className="flex justify-between items-center">
                <div>
                  <div className="font-semibold">Model X</div>
                  <div className="text-sm text-blue-600">产品 ID: P001</div>
                </div>
                <div className="text-lg font-semibold">500 units</div>
              </div>
            </div>

            <div className="bg-white p-4 rounded-lg">
              <div className="flex justify-between items-center">
                <div>
                  <div className="font-semibold">Model Y</div>
                  <div className="text-sm text-blue-600">产品 ID: P002</div>
                </div>
                <div className="text-lg font-semibold">300 units</div>
              </div>
            </div>
          </div>
        </div>

        <div className="flex gap-4">
          <Button variant="outline" className="flex-1 h-12">
            调整配额
          </Button>
          <Button className="flex-1 h-12">提交配额</Button>
        </div>
      </div>

      {/* Bottom Navigation */}
      <div className="fixed bottom-0 left-0 right-0 bg-white border-t">
        <div className="grid grid-cols-4">
          <Link href="/" className="flex flex-col items-center py-3 text-gray-600">
            <div className="w-6 h-6 mb-1">🏠</div>
            <span className="text-xs">首页</span>
          </Link>
          <Link href="/dealers" className="flex flex-col items-center py-3 text-gray-600">
            <div className="w-6 h-6 mb-1">👥</div>
            <span className="text-xs">经销商</span>
          </Link>
          <Link href="/quota" className="flex flex-col items-center py-3 text-blue-600">
            <div className="w-6 h-6 mb-1">💰</div>
            <span className="text-xs">配额</span>
          </Link>
          <Link href="/reports" className="flex flex-col items-center py-3 text-gray-600">
            <div className="w-6 h-6 mb-1">📊</div>
            <span className="text-xs">报告</span>
          </Link>
        </div>
      </div>
    </div>
  )
}
