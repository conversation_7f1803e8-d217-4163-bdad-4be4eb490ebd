# RegionTools

一个现代化的区域销售和库存管理系统，基于 Next.js 15 和 React 19 构建，提供完整的销售订单管理、配额分配、任务跟踪等功能。

## ✨ 功能特性

### 🏠 首页仪表板
- 待审批订单概览
- 到期任务提醒
- 最新公告展示
- 快速操作入口
- 最近活动记录

### 📋 订单管理
- 订单列表查看和搜索
- 订单详情查看
- 订单状态跟踪
- 分销商切换功能
- 订单筛选（状态、日期、客户）

### ✅ 任务管理
- 销售订单审批流程
- 待办任务列表
- 任务到期提醒
- 客户跟进管理

### 💰 配额管理
- 经销商配额查看
- 配额分配管理
- 产品配额统计
- 配额调整功能

### 📦 订单跟踪
- 订单号查询
- 历史订单记录
- 订单状态实时跟踪

## 🛠️ 技术栈

- **前端框架**: Next.js 15.2.4 (App Router)
- **UI 库**: React 19
- **样式**: Tailwind CSS 3.4.17
- **UI 组件**: Radix UI + shadcn/ui
- **图标**: Lucide React
- **表单**: React Hook Form + Zod
- **图表**: Recharts
- **通知**: Sonner
- **主题**: next-themes
- **语言**: TypeScript 5

## 📁 项目结构

```
regionTools/
├── app/                    # Next.js App Router 页面
│   ├── orders/            # 订单管理页面
│   ├── tasks/             # 任务管理页面
│   ├── quota/             # 配额管理页面
│   ├── tracking/          # 订单跟踪页面
│   ├── layout.tsx         # 根布局
│   └── page.tsx           # 首页
├── components/            # 可复用组件
│   ├── ui/               # shadcn/ui 组件库
│   └── theme-provider.tsx # 主题提供者
├── hooks/                # 自定义 React Hooks
├── lib/                  # 工具函数
├── public/               # 静态资源
└── styles/               # 全局样式
```

## 🚀 快速开始

### 环境要求

- Node.js 18+
- pnpm (推荐) 或 npm

### 安装依赖

```bash
# 使用 pnpm (推荐)
pnpm install

# 或使用 npm
npm install
```

### 开发环境

```bash
# 启动开发服务器
pnpm dev

# 或
npm run dev
```

访问 [http://localhost:3000](http://localhost:3000) 查看应用。

### 构建生产版本

```bash
# 构建应用
pnpm build

# 启动生产服务器
pnpm start
```

## 📜 可用脚本

- `pnpm dev` - 启动开发服务器
- `pnpm build` - 构建生产版本
- `pnpm start` - 启动生产服务器
- `pnpm lint` - 运行 ESLint 检查

## 📱 页面功能

### 首页 (`/`)
- 系统概览仪表板
- 待审批和到期任务统计
- 快速操作按钮
- 最近活动展示
- 底部导航栏

### 订单管理 (`/orders`)
- 订单列表展示
- 搜索和筛选功能
- 订单详情页面 (`/orders/[id]`)
- 分销商切换功能

### 任务管理 (`/tasks`)
- 销售订单审批列表
- 待办任务管理
- 任务到期提醒

### 配额管理 (`/quota`)
- 经销商信息展示
- 配额分配统计
- 产品配额管理
- 配额调整功能

### 订单跟踪 (`/tracking`)
- 订单号搜索
- 历史订单查看
- 订单状态跟踪

## 🎨 UI 组件

项目使用 [shadcn/ui](https://ui.shadcn.com/) 组件库，包含：

- 表单组件 (Input, Button, Select 等)
- 布局组件 (Card, Sheet, Dialog 等)
- 数据展示 (Table, Avatar, Badge 等)
- 反馈组件 (Toast, Alert, Progress 等)
- 导航组件 (Tabs, Breadcrumb 等)

所有组件都基于 Radix UI 构建，支持无障碍访问和键盘导航。

## 🔧 配置说明

### Tailwind CSS
项目使用 Tailwind CSS 进行样式管理，配置文件为 `tailwind.config.ts`，包含：
- 自定义颜色主题
- 响应式断点
- 动画效果
- shadcn/ui 组件样式

### Next.js 配置
`next.config.mjs` 包含：
- ESLint 和 TypeScript 构建配置
- 图片优化设置
- 开发和生产环境配置

## 🤝 贡献指南

1. Fork 本仓库
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🔗 相关链接

- [Next.js 文档](https://nextjs.org/docs)
- [Tailwind CSS 文档](https://tailwindcss.com/docs)
- [shadcn/ui 文档](https://ui.shadcn.com/)
- [Radix UI 文档](https://www.radix-ui.com/)