"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowLeft, User, Package, DollarSign } from "lucide-react"
import Link from "next/link"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

export default function OrderDetailsPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b px-4 py-4 flex items-center">
        <Link href="/orders">
          <ArrowLeft className="w-6 h-6 mr-4" />
        </Link>
        <h1 className="text-xl font-semibold">订单详情</h1>
      </div>

      <div className="p-4 space-y-6">
        {/* Order Info */}
        <div>
          <h2 className="text-2xl font-bold mb-4">订单 #12345</h2>

          <div className="space-y-4">
            <div className="flex items-center gap-4 bg-white p-4 rounded-lg">
              <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                <User className="w-6 h-6 text-gray-600" />
              </div>
              <div>
                <div className="font-medium">客户: Sarah Miller</div>
                <div className="text-sm text-blue-600">123 Main St, Anytown</div>
              </div>
            </div>

            <div className="flex items-center gap-4 bg-white p-4 rounded-lg">
              <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                <Package className="w-6 h-6 text-gray-600" />
              </div>
              <div>
                <div className="font-medium">订单状态: 处理中</div>
                <div className="text-sm text-gray-600">订单日期: 2024-01-15</div>
              </div>
            </div>

            <div className="flex items-center gap-4 bg-white p-4 rounded-lg">
              <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                <DollarSign className="w-6 h-6 text-gray-600" />
              </div>
              <div>
                <div className="font-medium">订单总额</div>
                <div className="text-sm text-blue-600">总计: $250.00</div>
              </div>
            </div>
          </div>
        </div>

        {/* Switch Order */}
        <div>
          <h2 className="text-2xl font-bold mb-4">切换订单</h2>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">分销商</label>
              <Select>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="选择分销商" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="distributor1">分销商 A</SelectItem>
                  <SelectItem value="distributor2">分销商 B</SelectItem>
                  <SelectItem value="distributor3">分销商 C</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        <Button className="w-full h-12 text-lg">确认切换</Button>
      </div>
    </div>
  )
}
