"use client"
import { <PERSON><PERSON><PERSON><PERSON>, FileText, CheckSquare } from "lucide-react"
import Link from "next/link"

export default function TasksPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b px-4 py-4 flex items-center">
        <Link href="/">
          <ArrowLeft className="w-6 h-6 mr-4" />
        </Link>
        <h1 className="text-xl font-semibold">待办</h1>
      </div>

      <div className="p-4 space-y-6">
        {/* Sales Order Approval */}
        <div>
          <h2 className="text-2xl font-bold mb-4">销售订单审批</h2>
          <div className="space-y-4">
            <div className="flex items-center gap-4 bg-white p-4 rounded-lg">
              <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                <FileText className="w-6 h-6 text-gray-600" />
              </div>
              <div className="flex-1">
                <div className="font-medium">审批销售订单</div>
                <div className="text-sm text-blue-600">订单 #123456</div>
              </div>
            </div>
            <div className="flex items-center gap-4 bg-white p-4 rounded-lg">
              <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                <FileText className="w-6 h-6 text-gray-600" />
              </div>
              <div className="flex-1">
                <div className="font-medium">审批销售订单</div>
                <div className="text-sm text-blue-600">订单 #789012</div>
              </div>
            </div>
          </div>
        </div>

        {/* Tasks */}
        <div>
          <h2 className="text-2xl font-bold mb-4">任务</h2>
          <div className="space-y-4">
            <div className="flex items-center gap-4 bg-white p-4 rounded-lg">
              <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                <CheckSquare className="w-6 h-6 text-gray-600" />
              </div>
              <div className="flex-1">
                <div className="font-medium">跟进客户</div>
                <div className="text-sm text-gray-600">到期: 2024-03-15</div>
              </div>
            </div>
            <div className="flex items-center gap-4 bg-white p-4 rounded-lg">
              <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                <CheckSquare className="w-6 h-6 text-gray-600" />
              </div>
              <div className="flex-1">
                <div className="font-medium">准备销售报告</div>
                <div className="text-sm text-gray-600">到期: 2024-03-20</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Navigation */}
      <div className="fixed bottom-0 left-0 right-0 bg-white border-t">
        <div className="grid grid-cols-4">
          <Link href="/" className="flex flex-col items-center py-3 text-gray-600">
            <div className="w-6 h-6 mb-1">🏠</div>
            <span className="text-xs">首页</span>
          </Link>
          <Link href="/tasks" className="flex flex-col items-center py-3 text-blue-600">
            <div className="w-6 h-6 mb-1">✓</div>
            <span className="text-xs">任务</span>
          </Link>
          <Link href="/reports" className="flex flex-col items-center py-3 text-gray-600">
            <div className="w-6 h-6 mb-1">📊</div>
            <span className="text-xs">报告</span>
          </Link>
          <Link href="/profile" className="flex flex-col items-center py-3 text-gray-600">
            <div className="w-6 h-6 mb-1">👤</div>
            <span className="text-xs">个人</span>
          </Link>
        </div>
      </div>
    </div>
  )
}
