"use client"

import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Search, Plus, ChevronDown } from "lucide-react"
import Link from "next/link"

export default function OrdersPage() {
  const orders = [
    { id: "123456", customer: "客户姓名", amount: "$100" },
    { id: "789012", customer: "另一个客户", amount: "$250" },
    { id: "345678", customer: "第三个客户", amount: "$150" },
    { id: "901234", customer: "第四个客户", amount: "$300" },
    { id: "567890", customer: "第五个客户", amount: "$200" },
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b px-4 py-4 flex items-center justify-between">
        <h1 className="text-xl font-semibold">订单</h1>
        <Plus className="w-6 h-6" />
      </div>

      <div className="p-4 space-y-4">
        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
          <Input placeholder="搜索订单" className="pl-10 bg-gray-100 border-0" />
        </div>

        {/* Filters */}
        <div className="flex gap-2">
          <Button variant="outline" size="sm" className="flex items-center gap-1">
            状态 <ChevronDown className="w-4 h-4" />
          </Button>
          <Button variant="outline" size="sm" className="flex items-center gap-1">
            日期 <ChevronDown className="w-4 h-4" />
          </Button>
          <Button variant="outline" size="sm" className="flex items-center gap-1">
            客户 <ChevronDown className="w-4 h-4" />
          </Button>
        </div>

        {/* Orders List */}
        <div className="space-y-4">
          {orders.map((order) => (
            <Link key={order.id} href={`/orders/${order.id}`}>
              <div className="bg-white p-4 rounded-lg flex items-center justify-between">
                <div>
                  <div className="font-medium">{order.customer}</div>
                  <div className="text-sm text-blue-600">订单 #{order.id}</div>
                </div>
                <div className="text-lg font-semibold">{order.amount}</div>
              </div>
            </Link>
          ))}
        </div>
      </div>

      {/* Bottom Navigation */}
      <div className="fixed bottom-0 left-0 right-0 bg-white border-t">
        <div className="grid grid-cols-4">
          <Link href="/" className="flex flex-col items-center py-3 text-gray-600">
            <div className="w-6 h-6 mb-1">🏠</div>
            <span className="text-xs">首页</span>
          </Link>
          <Link href="/orders" className="flex flex-col items-center py-3 text-blue-600">
            <div className="w-6 h-6 mb-1">📋</div>
            <span className="text-xs">订单</span>
          </Link>
          <Link href="/customers" className="flex flex-col items-center py-3 text-gray-600">
            <div className="w-6 h-6 mb-1">👥</div>
            <span className="text-xs">客户</span>
          </Link>
          <Link href="/settings" className="flex flex-col items-center py-3 text-gray-600">
            <div className="w-6 h-6 mb-1">⚙️</div>
            <span className="text-xs">设置</span>
          </Link>
        </div>
      </div>
    </div>
  )
}
