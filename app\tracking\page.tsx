"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { ArrowLeft, ArrowRight } from "lucide-react"
import Link from "next/link"

export default function TrackingPage() {
  const orders = [
    {
      date: "2023-09-20",
      number: "234567890123456",
    },
    {
      date: "2023-09-15",
      number: "345678901234567",
    },
    {
      date: "2023-09-10",
      number: "456789012345678",
    },
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b px-4 py-4 flex items-center">
        <Link href="/">
          <ArrowLeft className="w-6 h-6 mr-4" />
        </Link>
        <h1 className="text-xl font-semibold">订单跟踪</h1>
      </div>

      <div className="p-4 space-y-6">
        {/* Search */}
        <div className="flex gap-2">
          <Input placeholder="输入订单号" className="flex-1" />
          <Button>查询</Button>
        </div>

        {/* Or Divider */}
        <div className="text-center text-2xl font-bold">或者</div>

        {/* Recent Orders */}
        <div className="space-y-4">
          {orders.map((order, index) => (
            <div key={index} className="bg-white p-4 rounded-lg flex items-center justify-between">
              <div>
                <div className="text-sm text-gray-600">订单日期: {order.date}</div>
                <div className="text-blue-600">订单号: {order.number}</div>
              </div>
              <ArrowRight className="w-5 h-5 text-gray-400" />
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
